import React from 'react';
import {render, screen} from '@testing-library/react';
import DataStoreProvider, {useDataStoreContext, DataStoreContext} from '../../src/context/DataStoreProvider';
import {useNavigate} from 'react-router-dom';

const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => {
  const actual = jest.requireActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

let mockNavigateFn: jest.Mock;
beforeEach(() => {
  mockNavigate.mockClear();
  mockNavigateFn = require('react-router-dom').useNavigate();
});

// Provide a valid JwtUser mock for tests
const mockJwtUser = {
  exp: 0,
  iat: 0,
  auth_time: 0,
  jti: '',
  iss: '',
  aud: '',
  sub: '',
  typ: '',
  azp: '',
  nonce: '',
  session_state: '',
  acr: '',
  'allowed-origins': [],
  realm_access: {roles: []},
  resource_access: {account: {roles: []}},
  scope: '',
  user_name_hash: '',
  email_verified: false,
  is_nova_onboarded: false,
  user_id: '',
  name: '',
  tc_nova_version: 0,
  is_user_onboarded: false,
  preferred_username: '',
  given_name: '',
  family_name: '',
  email: null,
  group: [],
};

const baseRoleConfig = {
  user: mockJwtUser,
  riskAssessment: {
    canCreateNewTemplate: true,
    hasPermision: true,
  },
};

describe('DataStoreProvider', () => {
  const ga4EventTrigger = jest.fn();

  beforeEach(() => {
    mockNavigate.mockReset();
  });

  it('provides context and children', () => {
    function Consumer() {
      const ctx = useDataStoreContext();
      return <div data-testid="provided">{ctx && 'provided'}</div>;
    }
    render(
      <DataStoreProvider roleConfig={baseRoleConfig} ga4EventTrigger={ga4EventTrigger}>
        <Consumer />
      </DataStoreProvider>
    );
    expect(screen.getByTestId('provided')).toHaveTextContent('provided');
  });

  it('allows updating dataStore via setDataStore', () => {
    function Consumer() {
      const {dataStore, setDataStore} = useDataStoreContext();
      return (
        <>
          <button onClick={() => setDataStore(ds => ({...ds, riskCategoryList: [{id: 1, name: 'cat'}]}))}>update</button>
          <span data-testid="cat">{dataStore.riskCategoryList.length}</span>
        </>
      );
    }
    render(
      <DataStoreProvider roleConfig={baseRoleConfig} ga4EventTrigger={ga4EventTrigger}>
        <Consumer />
      </DataStoreProvider>
    );
    expect(screen.getByTestId('cat')).toHaveTextContent('0');
    screen.getByText('update').click();
    expect(screen.getByTestId('cat')).toHaveTextContent('1');
  });

  it('navigates to /home if hasPermision is false', () => {
    const roleConfig = {
      ...baseRoleConfig,
      riskAssessment: {
        ...baseRoleConfig.riskAssessment,
        hasPermision: false,
      },
    };
    function Consumer() {
      useDataStoreContext();
      return <div>test</div>;
    }
    render(
      <DataStoreProvider roleConfig={roleConfig} ga4EventTrigger={ga4EventTrigger}>
        <Consumer />
      </DataStoreProvider>
    );
    expect(mockNavigate).toHaveBeenCalledWith('/home');
  });

  it('throws if used outside provider', () => {
    // Suppress error output for this test
    const spy = jest.spyOn(console, 'error').mockImplementation(() => {});
    function Consumer() {
      useDataStoreContext();
      return <div>fail</div>;
    }
    expect(() => render(<Consumer />)).toThrow();
    spy.mockRestore();
  });

  it('memoizes context value', () => {
    let renderCount = 0;
    function Consumer() {
      renderCount++;
      useDataStoreContext();
      return <div>test</div>;
    }
    const {rerender} = render(
      <DataStoreProvider roleConfig={baseRoleConfig} ga4EventTrigger={ga4EventTrigger}>
        <Consumer />
      </DataStoreProvider>
    );
    rerender(
      <DataStoreProvider roleConfig={baseRoleConfig} ga4EventTrigger={ga4EventTrigger}>
        <Consumer />
      </DataStoreProvider>
    );
    expect(renderCount).toBe(2); // once per mount, once per rerender
  });
});
