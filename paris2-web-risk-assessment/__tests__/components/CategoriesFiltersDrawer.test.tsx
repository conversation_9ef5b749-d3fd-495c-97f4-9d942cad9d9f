import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CategoriesFiltersDrawer from '../../src/components/CategoriesFiltersDrawer';

// Mock SearchInput component
jest.mock('../../src/components/SearchInput', () => {
  return function MockSearchInput(props: any) {
    return (
      <input
        data-testid="mock-search-input"
        value={props.value}
        onChange={(e) => props.onSearch(e.target.value)}
        placeholder={props.placeholder}
      />
    );
  };
});

// Mock CheckboxComponent
jest.mock('../../src/components/CheckboxComponent', () => {
  return function MockCheckboxComponent(props: any) {
    return (
      <div>
        <input
          type="checkbox"
          data-testid={props.id}
          checked={props.checked}
          onChange={props.onChange}
        />
        <label>{props.label}</label>
      </div>
    );
  };
});

// Mock TruncateBasicText
jest.mock('../../src/components/TruncateBasicText', () => {
  return function MockTruncateText(props: any) {
    return <span>{props.text}</span>;
  };
});

// Mock Drawer component
const mockCloseDrawer = jest.fn();
let mockOnClose: (() => void) | undefined;

jest.mock('../../src/components/Drawer', () => {
  return function MockDrawer(props: any) {
    // Capture the onClose prop so we can call it in tests
    mockOnClose = props.onClose;

    return (
      <div>
        {props.trigger}
        <div data-testid="drawer-content">
          {props.children({ closeDrawer: mockCloseDrawer })}
        </div>
      </div>
    );
  };
});

// Mock icons
jest.mock('../../src/components/icons', () => ({
  PlusIcon: function MockPlusIcon() {
    return <span data-testid="plus-icon">+</span>;
  }
}));

const mockContext = {
  dataStore: {
    riskCategoryList: [
      { id: 1, name: 'Risk Category 1' },
      { id: 2, name: 'Risk Category 2' },
      { id: 3, name: 'Another Risk Category' },
    ],
    hazardsList: [
      { id: 10, name: 'Hazard 1' },
      { id: 20, name: 'Hazard 2' },
      { id: 30, name: 'Another Hazard' },
    ],
  },
};

const mockUseDataStoreContext = jest.fn();

jest.mock('../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

describe('CategoriesFiltersDrawer', () => {
  const defaultFilters = { ra_categories: [1], hazard_categories: [10] };
  let onFilterChange: jest.Mock;

  beforeEach(() => {
    onFilterChange = jest.fn();
    mockCloseDrawer.mockClear();
    mockOnClose = undefined;
    const { useDataStoreContext } = require('../../src/context');
    useDataStoreContext.mockReturnValue(mockContext);
  });

  describe('Basic Rendering', () => {
    it('renders trigger button with "More Filters" when no filters are selected', () => {
      render(
        <CategoriesFiltersDrawer
          filters={{ ra_categories: null, hazard_categories: null }}
          onFilterChange={onFilterChange}
        />
      );

      expect(screen.getByText('More Filters')).toBeInTheDocument();
      expect(screen.getByTestId('plus-icon')).toBeInTheDocument();
    });

    it('renders trigger button with filter count when filters are selected', () => {
      render(
        <CategoriesFiltersDrawer
          filters={{ ra_categories: [1, 2], hazard_categories: [10] }}
          onFilterChange={onFilterChange}
        />
      );

      expect(screen.getByText('Filters')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('renders drawer content with both filter sections', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      expect(screen.getByTestId('drawer-content')).toBeInTheDocument();
      expect(screen.getByText('Filter by R.A. Categories')).toBeInTheDocument();
      expect(screen.getByText('Filter by Hazard Categories')).toBeInTheDocument();
    });

    it('renders Clear and Apply buttons', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      expect(screen.getByText('Clear')).toBeInTheDocument();
      expect(screen.getByText('Apply')).toBeInTheDocument();
    });
  });

  describe('Filter Count Calculation', () => {
    it('shows correct count with only RA categories', () => {
      render(
        <CategoriesFiltersDrawer
          filters={{ ra_categories: [1, 2, 3], hazard_categories: null }}
          onFilterChange={onFilterChange}
        />
      );

      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('shows correct count with only hazard categories', () => {
      render(
        <CategoriesFiltersDrawer
          filters={{ ra_categories: null, hazard_categories: [10, 20] }}
          onFilterChange={onFilterChange}
        />
      );

      expect(screen.getByText('2')).toBeInTheDocument();
    });

    it('shows correct count with both filter types', () => {
      render(
        <CategoriesFiltersDrawer
          filters={{ ra_categories: [1], hazard_categories: [10, 20] }}
          onFilterChange={onFilterChange}
        />
      );

      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('handles empty arrays correctly', () => {
      render(
        <CategoriesFiltersDrawer
          filters={{ ra_categories: [], hazard_categories: [] }}
          onFilterChange={onFilterChange}
        />
      );

      expect(screen.getByText('More Filters')).toBeInTheDocument();
    });
  });

  describe('Button Functionality', () => {
    it('calls onFilterChange with null values when Clear is clicked', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      fireEvent.click(screen.getByText('Clear'));

      expect(onFilterChange).toHaveBeenCalledWith('ra_categories', null);
      expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', null);
      expect(mockCloseDrawer).toHaveBeenCalled();
    });

    it('calls onFilterChange with current state when Apply is clicked', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      fireEvent.click(screen.getByText('Apply'));

      expect(onFilterChange).toHaveBeenCalledWith('ra_categories', [1]);
      expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', [10]);
      expect(mockCloseDrawer).toHaveBeenCalled();
    });

    it('resets internal state when component receives new props', () => {
      const { rerender } = render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // Verify initial state
      expect(screen.getByTestId('category-checked-1')).toBeChecked();
      expect(screen.getByTestId('category-checked-2')).not.toBeChecked();

      // Simulate changing a checkbox (this would normally update internal state)
      const checkbox = screen.getByTestId('category-checked-2');
      fireEvent.click(checkbox);

      // Now checkbox 2 should be checked due to internal state change
      expect(screen.getByTestId('category-checked-2')).toBeChecked();

      // Rerender with same props - internal state should persist
      rerender(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // The internal state should still show the change
      expect(screen.getByTestId('category-checked-2')).toBeChecked();
    });

    it('resets internal state when drawer is closed via onClose', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // Verify initial state
      expect(screen.getByTestId('category-checked-1')).toBeChecked();
      expect(screen.getByTestId('category-checked-2')).not.toBeChecked();
      expect(screen.getByTestId('hazard-checked-10')).toBeChecked();
      expect(screen.getByTestId('hazard-checked-20')).not.toBeChecked();

      // Make changes to internal state
      fireEvent.click(screen.getByTestId('category-checked-2')); // Check it
      fireEvent.click(screen.getByTestId('category-checked-1')); // Uncheck it
      fireEvent.click(screen.getByTestId('hazard-checked-20')); // Check it
      fireEvent.click(screen.getByTestId('hazard-checked-10')); // Uncheck it

      // Verify changes were made
      expect(screen.getByTestId('category-checked-1')).not.toBeChecked();
      expect(screen.getByTestId('category-checked-2')).toBeChecked();
      expect(screen.getByTestId('hazard-checked-10')).not.toBeChecked();
      expect(screen.getByTestId('hazard-checked-20')).toBeChecked();

      // Simulate drawer close by calling the onClose handler directly
      // The mockOnClose should be the handleResetFilters function
      expect(mockOnClose).toBeDefined();
      mockOnClose!();

      // After onClose is called, the internal state should reset to match the original filters
      expect(screen.getByTestId('category-checked-1')).toBeChecked(); // Back to original
      expect(screen.getByTestId('category-checked-2')).not.toBeChecked(); // Back to original
      expect(screen.getByTestId('hazard-checked-10')).toBeChecked(); // Back to original
      expect(screen.getByTestId('hazard-checked-20')).not.toBeChecked(); // Back to original
    });
  });

  describe('Risk Categories Filter', () => {
    it('renders all risk categories as checkboxes', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      expect(screen.getByTestId('category-checked-1')).toBeInTheDocument();
      expect(screen.getByTestId('category-checked-2')).toBeInTheDocument();
      expect(screen.getByTestId('category-checked-3')).toBeInTheDocument();
    });

    it('shows correct checked state for risk categories', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      expect(screen.getByTestId('category-checked-1')).toBeChecked();
      expect(screen.getByTestId('category-checked-2')).not.toBeChecked();
      expect(screen.getByTestId('category-checked-3')).not.toBeChecked();
    });

    it('handles risk category checkbox changes', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // Click unchecked checkbox to check it
      fireEvent.click(screen.getByTestId('category-checked-2'));

      // Click Apply to see the change
      fireEvent.click(screen.getByText('Apply'));

      expect(onFilterChange).toHaveBeenCalledWith('ra_categories', [1, 2]);
    });

    it('handles unchecking risk category checkbox', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // Click checked checkbox to uncheck it
      fireEvent.click(screen.getByTestId('category-checked-1'));

      // Click Apply to see the change
      fireEvent.click(screen.getByText('Apply'));

      expect(onFilterChange).toHaveBeenCalledWith('ra_categories', []);
    });

    it('renders search input for risk categories', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      const searchInputs = screen.getAllByTestId('mock-search-input');
      const raSearchInput = searchInputs.find(input =>
        input.getAttribute('placeholder') === 'Search RA Category'
      );

      expect(raSearchInput).toBeInTheDocument();
    });
  });

  describe('Hazard Categories Filter', () => {
    it('renders all hazard categories as checkboxes', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      expect(screen.getByTestId('hazard-checked-10')).toBeInTheDocument();
      expect(screen.getByTestId('hazard-checked-20')).toBeInTheDocument();
      expect(screen.getByTestId('hazard-checked-30')).toBeInTheDocument();
    });

    it('shows correct checked state for hazard categories', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      expect(screen.getByTestId('hazard-checked-10')).toBeChecked();
      expect(screen.getByTestId('hazard-checked-20')).not.toBeChecked();
      expect(screen.getByTestId('hazard-checked-30')).not.toBeChecked();
    });

    it('handles hazard category checkbox changes', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // Click unchecked checkbox to check it
      fireEvent.click(screen.getByTestId('hazard-checked-20'));

      // Click Apply to see the change
      fireEvent.click(screen.getByText('Apply'));

      expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', [10, 20]);
    });

    it('handles unchecking hazard category checkbox', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // Click checked checkbox to uncheck it
      fireEvent.click(screen.getByTestId('hazard-checked-10'));

      // Click Apply to see the change
      fireEvent.click(screen.getByText('Apply'));

      expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', []);
    });

    it('renders search input for hazard categories', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      const searchInputs = screen.getAllByTestId('mock-search-input');
      const hazardSearchInput = searchInputs.find(input =>
        input.getAttribute('placeholder') === 'Search Hazard Categories'
      );

      expect(hazardSearchInput).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('filters risk categories based on search input', async () => {
      // Mock context with searchable data
      const searchableContext = {
        dataStore: {
          riskCategoryList: [
            { id: 1, name: 'Fire Risk' },
            { id: 2, name: 'Water Risk' },
            { id: 3, name: 'Environmental Risk' },
          ],
          hazardsList: mockContext.dataStore.hazardsList,
        },
      };

      const { useDataStoreContext } = require('../../src/context');
      useDataStoreContext.mockReturnValue(searchableContext);

      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      const searchInputs = screen.getAllByTestId('mock-search-input');
      const raSearchInput = searchInputs.find(input =>
        input.getAttribute('placeholder') === 'Search RA Category'
      );

      // Search for "fire"
      fireEvent.change(raSearchInput!, { target: { value: 'fire' } });

      // Should show Fire Risk but not Water Risk or Environmental Risk
      expect(screen.getByTestId('category-checked-1')).toBeInTheDocument();
      expect(screen.queryByTestId('category-checked-2')).not.toBeInTheDocument();
      expect(screen.queryByTestId('category-checked-3')).not.toBeInTheDocument();
    });

    it('filters hazard categories based on search input', async () => {
      // Mock context with searchable data
      const searchableContext = {
        dataStore: {
          riskCategoryList: mockContext.dataStore.riskCategoryList,
          hazardsList: [
            { id: 10, name: 'Chemical Hazard' },
            { id: 20, name: 'Physical Hazard' },
            { id: 30, name: 'Biological Hazard' },
          ],
        },
      };

      const { useDataStoreContext } = require('../../src/context');
      useDataStoreContext.mockReturnValue(searchableContext);

      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      const searchInputs = screen.getAllByTestId('mock-search-input');
      const hazardSearchInput = searchInputs.find(input =>
        input.getAttribute('placeholder') === 'Search Hazard Categories'
      );

      // Search for "chemical"
      fireEvent.change(hazardSearchInput!, { target: { value: 'chemical' } });

      // Should show Chemical Hazard but not others
      expect(screen.getByTestId('hazard-checked-10')).toBeInTheDocument();
      expect(screen.queryByTestId('hazard-checked-20')).not.toBeInTheDocument();
      expect(screen.queryByTestId('hazard-checked-30')).not.toBeInTheDocument();
    });

    it('shows "No results found" message when search yields no results for RA categories', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      const searchInputs = screen.getAllByTestId('mock-search-input');
      const raSearchInput = searchInputs.find(input =>
        input.getAttribute('placeholder') === 'Search RA Category'
      );

      // Search for something that doesn't exist
      fireEvent.change(raSearchInput!, { target: { value: 'nonexistent' } });

      expect(screen.getByText("No results found for 'nonexistent'.")).toBeInTheDocument();
    });

    it('shows "No results found" message when search yields no results for hazard categories', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      const searchInputs = screen.getAllByTestId('mock-search-input');
      const hazardSearchInput = searchInputs.find(input =>
        input.getAttribute('placeholder') === 'Search Hazard Categories'
      );

      // Search for something that doesn't exist
      fireEvent.change(hazardSearchInput!, { target: { value: 'nonexistent' } });

      expect(screen.getByText("No results found for 'nonexistent'.")).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty risk category list', () => {
      const emptyContext = {
        dataStore: {
          riskCategoryList: [],
          hazardsList: mockContext.dataStore.hazardsList,
        },
      };

      const { useDataStoreContext } = require('../../src/context');
      useDataStoreContext.mockReturnValue(emptyContext);

      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      expect(screen.getByText('No categories found.')).toBeInTheDocument();
    });

    it('handles empty hazard list', () => {
      const emptyContext = {
        dataStore: {
          riskCategoryList: mockContext.dataStore.riskCategoryList,
          hazardsList: [],
        },
      };

      const { useDataStoreContext } = require('../../src/context');
      useDataStoreContext.mockReturnValue(emptyContext);

      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      expect(screen.getByText('No hazard categories found.')).toBeInTheDocument();
    });

    it('handles null filter values correctly', () => {
      render(
        <CategoriesFiltersDrawer
          filters={{ ra_categories: null, hazard_categories: null }}
          onFilterChange={onFilterChange}
        />
      );

      // All checkboxes should be unchecked
      expect(screen.getByTestId('category-checked-1')).not.toBeChecked();
      expect(screen.getByTestId('category-checked-2')).not.toBeChecked();
      expect(screen.getByTestId('category-checked-3')).not.toBeChecked();
      expect(screen.getByTestId('hazard-checked-10')).not.toBeChecked();
      expect(screen.getByTestId('hazard-checked-20')).not.toBeChecked();
      expect(screen.getByTestId('hazard-checked-30')).not.toBeChecked();
    });

    it('handles undefined filter values correctly', () => {
      render(
        <CategoriesFiltersDrawer
          filters={{}}
          onFilterChange={onFilterChange}
        />
      );

      // All checkboxes should be unchecked
      expect(screen.getByTestId('category-checked-1')).not.toBeChecked();
      expect(screen.getByTestId('hazard-checked-10')).not.toBeChecked();
    });

    it('handles case-insensitive search', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      const searchInputs = screen.getAllByTestId('mock-search-input');
      const raSearchInput = searchInputs.find(input =>
        input.getAttribute('placeholder') === 'Search RA Category'
      );

      // Search with different cases
      fireEvent.change(raSearchInput!, { target: { value: 'RISK' } });

      // Should still find categories with "risk" in lowercase
      expect(screen.getByTestId('category-checked-1')).toBeInTheDocument();
    });

    it('clears search when empty string is provided', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      const searchInputs = screen.getAllByTestId('mock-search-input');
      const raSearchInput = searchInputs.find(input =>
        input.getAttribute('placeholder') === 'Search RA Category'
      );

      // First search for something
      fireEvent.change(raSearchInput!, { target: { value: 'risk' } });

      // Then clear the search
      fireEvent.change(raSearchInput!, { target: { value: '' } });

      // Should show all categories again
      expect(screen.getByTestId('category-checked-1')).toBeInTheDocument();
      expect(screen.getByTestId('category-checked-2')).toBeInTheDocument();
      expect(screen.getByTestId('category-checked-3')).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    it('maintains separate state for RA and hazard categories', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // Change RA category
      fireEvent.click(screen.getByTestId('category-checked-2'));

      // Change hazard category
      fireEvent.click(screen.getByTestId('hazard-checked-20'));

      // Apply changes
      fireEvent.click(screen.getByText('Apply'));

      // Should update both categories independently
      expect(onFilterChange).toHaveBeenCalledWith('ra_categories', [1, 2]);
      expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', [10, 20]);
    });

    it('resets both categories when clear is clicked', () => {
      render(
        <CategoriesFiltersDrawer filters={defaultFilters} onFilterChange={onFilterChange} />
      );

      // Make some changes first
      fireEvent.click(screen.getByTestId('category-checked-2'));
      fireEvent.click(screen.getByTestId('hazard-checked-20'));

      // Clear all
      fireEvent.click(screen.getByText('Clear'));

      expect(onFilterChange).toHaveBeenCalledWith('ra_categories', null);
      expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', null);
    });
  });
});
